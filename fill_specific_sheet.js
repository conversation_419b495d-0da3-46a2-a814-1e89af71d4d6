// Fill form from a specific Excel worksheet
const ExcelToFormFiller = require('./excel_to_form_filler.js');

function fillFromSpecificSheet(sheetName, outputFileName = null) {
  try {
    console.log(`=== Filling form from worksheet: ${sheetName} ===\n`);
    
    // Create filler instance
    const filler = new ExcelToFormFiller();
    
    // Check if worksheet exists
    if (!filler.workbook.SheetNames.includes(sheetName)) {
      console.log(`❌ Worksheet '${sheetName}' not found.`);
      console.log(`Available worksheets: ${filler.workbook.SheetNames.join(', ')}`);
      return false;
    }
    
    // Fill form with data from specific worksheet
    const result = filler.fillFormFromExcel(sheetName);
    
    if (result.success) {
      console.log(`✅ Successfully updated ${result.successCount} fields from worksheet '${sheetName}'`);
      
      if (result.errorCount > 0) {
        console.log(`⚠️  ${result.errorCount} errors occurred`);
      }
      
      // Save with custom filename if provided
      const outputPath = outputFileName || `form_filled_from_${sheetName}.json`;
      filler.saveUpdatedForm(outputPath);
      
      console.log(`💾 Updated form saved to: ${outputPath}`);
      
      // Show some sample data
      console.log('\n📊 Sample data filled:');
      result.updates.slice(0, 5).forEach(update => {
        console.log(`   ${update.key.split(':').pop()} = ${update.value} (from ${update.cell})`);
      });
      
      return true;
    } else {
      console.log(`❌ Failed: ${result.error}`);
      return false;
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    return false;
  }
}

// Command line usage
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Usage: node fill_specific_sheet.js <worksheet_name> [output_filename]');
    console.log('\nExample: node fill_specific_sheet.js BAC');
    console.log('Example: node fill_specific_sheet.js DUKE form_duke.json');
    
    // Show available worksheets
    try {
      const filler = new ExcelToFormFiller();
      console.log(`\nAvailable worksheets: ${filler.workbook.SheetNames.join(', ')}`);
    } catch (error) {
      console.log('\nMake sure A1.1 Workbook.xlsx exists in the current directory.');
    }
  } else {
    const sheetName = args[0];
    const outputFileName = args[1];
    fillFromSpecificSheet(sheetName, outputFileName);
  }
}

module.exports = fillFromSpecificSheet;
