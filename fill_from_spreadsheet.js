// Script to fill form.json values from spreadsheet using the mapping
const fs = require('fs');
const FormFiller = require('./form_filler.js');

// This is a template script showing how to use the mapping
// You'll need to adapt this based on your spreadsheet reading method

class SpreadsheetFormFiller {
  constructor(formDataPath, mappingPath) {
    // Load form data
    this.formData = JSON.parse(fs.readFileSync(formDataPath, 'utf8'));
    this.filler = new FormFiller(this.formData);
    
    // Load mapping
    this.mapping = JSON.parse(fs.readFileSync(mappingPath, 'utf8'));
    
    console.log(`Loaded ${this.mapping.length} mappings`);
  }

  // Method to fill values from spreadsheet data
  // spreadsheetData should be an object with cell references as keys and values as values
  // Example: { "A2": 85, "A3": 0, "A4": 85, ... }
  fillFromSpreadsheetData(spreadsheetData) {
    let successCount = 0;
    let errorCount = 0;
    const errors = [];

    this.mapping.forEach(mapping => {
      const cellValue = spreadsheetData[mapping.cell];
      
      if (cellValue !== undefined && cellValue !== null && cellValue !== '') {
        try {
          // Convert to number if it's a numeric string
          let value = cellValue;
          if (typeof cellValue === 'string' && !isNaN(cellValue) && cellValue.trim() !== '') {
            value = parseFloat(cellValue);
          }
          
          const success = this.filler.setValue(mapping.key, value);
          if (success) {
            successCount++;
            console.log(`✓ Set ${mapping.key} = ${value} (from ${mapping.cell})`);
          } else {
            errorCount++;
            errors.push(`Failed to set ${mapping.key} from ${mapping.cell}`);
          }
        } catch (error) {
          errorCount++;
          errors.push(`Error setting ${mapping.key} from ${mapping.cell}: ${error.message}`);
        }
      }
    });

    console.log(`\nResults: ${successCount} successful, ${errorCount} errors`);
    if (errors.length > 0) {
      console.log('\nErrors:');
      errors.forEach(error => console.log(`✗ ${error}`));
    }

    return { successCount, errorCount, errors };
  }

  // Method to create a template CSV file for the spreadsheet
  createSpreadsheetTemplate(outputPath = 'spreadsheet_template.csv') {
    let csvContent = 'Cell,Path,Current Value,Instructions\n';
    
    this.mapping.forEach(mapping => {
      const currentValue = this.filler.getValue(mapping.key) || 0;
      csvContent += `${mapping.cell},"${mapping.key}",${currentValue},"Enter value for ${mapping.key}"\n`;
    });

    fs.writeFileSync(outputPath, csvContent);
    console.log(`Spreadsheet template saved to ${outputPath}`);
  }

  // Save the updated form data
  saveUpdatedForm(outputPath = 'form_updated.json') {
    fs.writeFileSync(outputPath, JSON.stringify(this.formData, null, 2));
    console.log(`Updated form saved to ${outputPath}`);
  }

  // Get mapping for a specific cell
  getMappingForCell(cell) {
    return this.mapping.find(m => m.cell === cell);
  }

  // Get mapping for a specific key
  getMappingForKey(key) {
    return this.mapping.find(m => m.key === key);
  }

  // Get all mappings for a specific section
  getMappingsForSection(sectionName) {
    return this.mapping.filter(m => m.key.startsWith(sectionName + ':'));
  }
}

// Example usage:
function exampleUsage() {
  // Create filler instance
  const filler = new SpreadsheetFormFiller('form.json', 'simple_mapping.json');

  // Example spreadsheet data (you would get this from reading your actual spreadsheet)
  const exampleSpreadsheetData = {
    'A2': 85,   // In-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmen
    'A3': 0,    // In-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmenPartTime
    'A4': 85,   // In-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmenFTE
    'A5': 40,   // In-State:Undergrad:Men:Other Freshmen:otherFreshmen
    'A6': 3,    // In-State:Undergrad:Men:Other Freshmen:otherFreshmenPartTime
    'A7': 41.5, // In-State:Undergrad:Men:Other Freshmen:otherFreshmenFTE
    // ... add more values as needed
  };

  // Fill values from spreadsheet data
  console.log('Filling values from spreadsheet data...');
  const results = filler.fillFromSpreadsheetData(exampleSpreadsheetData);

  // Save updated form
  filler.saveUpdatedForm();

  // Create template for spreadsheet
  filler.createSpreadsheetTemplate();

  return filler;
}

// Export for use as module
module.exports = SpreadsheetFormFiller;

// Run example if this file is executed directly
if (require.main === module) {
  exampleUsage();
}
