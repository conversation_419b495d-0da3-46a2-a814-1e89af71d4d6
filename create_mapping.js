// Script to create spreadsheet mapping from form.json
const fs = require('fs');
const FormFiller = require('./form_filler.js');

// Load the form data
const formData = JSON.parse(fs.readFileSync('form.json', 'utf8'));

// Create form filler instance
const filler = new FormFiller(formData);

// Get all paths
const allPaths = filler.getAllPaths();

// Create mapping structure
const mapping = {
  "metadata": {
    "description": "Mapping between form.json keys and spreadsheet cell locations",
    "created": new Date().toISOString(),
    "totalFields": allPaths.length
  },
  "mappings": {}
};

// Define spreadsheet structure based on typical enrollment forms
// This is a template - you'll need to adjust cell references to match your actual spreadsheet

let currentRow = 2; // Start from row 2 (assuming row 1 has headers)

// Group paths by section for better organization
const sections = {
  "In-State": [],
  "Out-of-State": [],
  "Totals": [],
  "institution": [],
  "reportingPerson": [],
  "submission": [],
  "Enrollment": []
};

// Categorize paths
allPaths.forEach(item => {
  const pathParts = item.path.split(':');
  const section = pathParts[0];
  
  if (sections[section]) {
    sections[section].push(item);
  } else {
    // Handle any uncategorized items
    if (!sections["Other"]) sections["Other"] = [];
    sections["Other"].push(item);
  }
});

// Create mappings with more realistic spreadsheet layout
// Assuming a typical enrollment form spreadsheet structure

// Define column mappings for different data types
const columnMap = {
  "In-State": { startCol: "B", startRow: 5 },
  "Out-of-State": { startCol: "C", startRow: 5 },
  "Totals": { startCol: "D", startRow: 5 },
  "institution": { startCol: "F", startRow: 2 },
  "reportingPerson": { startCol: "F", startRow: 8 },
  "submission": { startCol: "F", startRow: 15 },
  "Enrollment": { startCol: "F", startRow: 17 }
};

// Helper function to convert column letter to number and back
function getColumnLetter(colIndex) {
  let result = '';
  while (colIndex > 0) {
    colIndex--;
    result = String.fromCharCode(65 + (colIndex % 26)) + result;
    colIndex = Math.floor(colIndex / 26);
  }
  return result;
}

function getColumnIndex(letter) {
  let result = 0;
  for (let i = 0; i < letter.length; i++) {
    result = result * 26 + (letter.charCodeAt(i) - 64);
  }
  return result;
}

// Create mappings for each section with realistic cell references
Object.keys(sections).forEach(sectionName => {
  const items = sections[sectionName];
  const config = columnMap[sectionName] || { startCol: "A", startRow: 2 };

  let currentRowForSection = config.startRow;
  let currentColIndex = getColumnIndex(config.startCol);

  items.forEach((item, index) => {
    // For enrollment data, organize by categories
    let cellRef;

    if (sectionName === "In-State" || sectionName === "Out-of-State") {
      // Organize enrollment data in a grid pattern
      // Rows represent different student categories, columns represent In-State vs Out-of-State
      const pathParts = item.path.split(':');

      // Create a more structured layout based on the hierarchy
      if (pathParts.includes("Men")) {
        currentRowForSection = config.startRow + (index % 50); // Spread men's data
      } else if (pathParts.includes("Women")) {
        currentRowForSection = config.startRow + 50 + (index % 50); // Women's data below men's
      }

      cellRef = `${config.startCol}${currentRowForSection}`;
    } else {
      // For other sections, use simple sequential layout
      cellRef = `${config.startCol}${currentRowForSection}`;
      currentRowForSection++;
    }

    mapping.mappings[item.key] = {
      "path": item.path,
      "label": item.label,
      "type": item.type,
      "currentValue": item.value,
      "spreadsheetCell": cellRef,
      "section": sectionName,
      "comments": item.comments || "",
      "hierarchy": item.path.split(':')
    };
  });
});

// Save the mapping file
fs.writeFileSync('spreadsheet_mapping.json', JSON.stringify(mapping, null, 2));

console.log(`Created mapping file with ${allPaths.length} field mappings`);
console.log('Sections found:', Object.keys(sections));
console.log('Mapping saved to spreadsheet_mapping.json');

// Also create a CSV template for the spreadsheet
let csvContent = "Cell,Key,Path,Label,Type,CurrentValue,Comments\n";

Object.entries(mapping.mappings).forEach(([key, data]) => {
  const csvRow = [
    data.spreadsheetCell,
    key,
    `"${data.path}"`,
    `"${data.label}"`,
    data.type,
    data.currentValue,
    `"${data.comments}"`
  ].join(',');
  csvContent += csvRow + '\n';
});

fs.writeFileSync('spreadsheet_template.csv', csvContent);
console.log('CSV template saved to spreadsheet_template.csv');
