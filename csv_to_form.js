// Script to fill form from CSV data using the simple mapping
const fs = require('fs');
const SpreadsheetFormFiller = require('./fill_from_spreadsheet.js');

// Function to parse CSV and convert to cell-value mapping
function parseCSV(csvContent) {
  const lines = csvContent.split('\n');
  const cellData = {};
  
  // Skip header row, process data rows
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line) {
      const parts = line.split(',');
      if (parts.length >= 2) {
        const cell = parts[0].trim();
        const value = parts[1].trim();
        
        // Convert to number if possible
        if (!isNaN(value) && value !== '') {
          cellData[cell] = parseFloat(value);
        } else {
          cellData[cell] = value;
        }
      }
    }
  }
  
  return cellData;
}

// Function to create sample CSV data
function createSampleCSV() {
  const sampleData = `Cell,Value
A2,85
A3,0
A4,85
A5,40
A6,3
A7,41.5
A8,65
A9,10
A10,70.25
A11,55
A12,8
A13,59
A14,45
A15,5
A16,47.5
A17,35
A18,3
A19,36.5
A20,82
A21,0
A22,82
A23,38
A24,2
A25,39`;

  fs.writeFileSync('sample_data.csv', sampleData);
  console.log('Sample CSV data created: sample_data.csv');
  return sampleData;
}

// Main function to demonstrate CSV to form filling
function fillFormFromCSV(csvFilePath = 'sample_data.csv') {
  try {
    // Create sample data if file doesn't exist
    if (!fs.existsSync(csvFilePath)) {
      console.log(`CSV file ${csvFilePath} not found. Creating sample data...`);
      createSampleCSV();
    }

    // Read CSV file
    const csvContent = fs.readFileSync(csvFilePath, 'utf8');
    console.log(`Reading CSV data from ${csvFilePath}...`);

    // Parse CSV to get cell-value mapping
    const cellData = parseCSV(csvContent);
    console.log(`Parsed ${Object.keys(cellData).length} cell values`);

    // Create form filler
    const filler = new SpreadsheetFormFiller('form.json', 'simple_mapping.json');

    // Fill form with CSV data
    console.log('\nFilling form with CSV data...');
    const results = filler.fillFromSpreadsheetData(cellData);

    // Save updated form
    filler.saveUpdatedForm('form_filled_from_csv.json');

    console.log('\n=== Summary ===');
    console.log(`Successfully filled ${results.successCount} fields`);
    console.log(`Errors: ${results.errorCount}`);
    console.log('Updated form saved to: form_filled_from_csv.json');

    return results;

  } catch (error) {
    console.error('Error filling form from CSV:', error.message);
    return null;
  }
}

// Export functions
module.exports = {
  parseCSV,
  createSampleCSV,
  fillFormFromCSV
};

// Run if executed directly
if (require.main === module) {
  fillFormFromCSV();
}
