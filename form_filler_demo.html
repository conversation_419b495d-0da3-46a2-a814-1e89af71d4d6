<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Filler Demo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .input-group { margin: 10px 0; }
        label { display: inline-block; width: 300px; font-weight: bold; }
        input { width: 100px; padding: 5px; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .output { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; }
        .path-list { max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Form Filler Demo</h1>
        
        <div class="section">
            <h2>Load Form Data</h2>
            <input type="file" id="fileInput" accept=".json">
            <button onclick="loadSampleData()">Load Sample Data</button>
            <div id="loadStatus" class="output"></div>
        </div>

        <div class="section">
            <h2>Set Individual Values</h2>
            <div class="input-group">
                <label>Path:</label>
                <input type="text" id="singlePath" placeholder="e.g., In-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmen" style="width: 400px;">
            </div>
            <div class="input-group">
                <label>Value:</label>
                <input type="number" id="singleValue" placeholder="85">
                <button onclick="setSingleValue()">Set Value</button>
            </div>
            <div id="singleResult" class="output"></div>
        </div>

        <div class="section">
            <h2>Set Multiple Values</h2>
            <textarea id="multipleValues" rows="10" cols="80" placeholder="Enter JSON object with path:value pairs, e.g.:
{
  &quot;In-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmen&quot;: 85,
  &quot;In-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmenPartTime&quot;: 0,
  &quot;In-State:Undergrad:Women:First-Time Freshmen:firstTimeFreshmen&quot;: 82
}"></textarea>
            <br>
            <button onclick="setMultipleValues()">Set Multiple Values</button>
            <div id="multipleResult" class="output"></div>
        </div>

        <div class="section">
            <h2>Get Value</h2>
            <div class="input-group">
                <label>Path:</label>
                <input type="text" id="getPath" placeholder="e.g., In-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmen" style="width: 400px;">
                <button onclick="getValue()">Get Value</button>
            </div>
            <div id="getResult" class="output"></div>
        </div>

        <div class="section">
            <h2>Available Paths</h2>
            <button onclick="showAllPaths()">Show All Paths</button>
            <button onclick="downloadUpdatedForm()">Download Updated Form</button>
            <div id="pathsList" class="path-list"></div>
        </div>
    </div>

    <script src="form_filler.js"></script>
    <script>
        let formData = null;
        let filler = null;

        // Load form data from file
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        formData = JSON.parse(e.target.result);
                        filler = new FormFiller(formData);
                        document.getElementById('loadStatus').innerHTML = '<span style="color: green;">✓ Form data loaded successfully!</span>';
                    } catch (error) {
                        document.getElementById('loadStatus').innerHTML = '<span style="color: red;">✗ Error loading form data: ' + error.message + '</span>';
                    }
                };
                reader.readAsText(file);
            }
        });

        // Load sample data for demo
        function loadSampleData() {
            // This would normally load your actual form.json
            document.getElementById('loadStatus').innerHTML = '<span style="color: orange;">Please upload your form.json file using the file input above.</span>';
        }

        function setSingleValue() {
            if (!filler) {
                document.getElementById('singleResult').innerHTML = '<span style="color: red;">Please load form data first.</span>';
                return;
            }

            const path = document.getElementById('singlePath').value;
            const value = parseFloat(document.getElementById('singleValue').value);

            if (!path) {
                document.getElementById('singleResult').innerHTML = '<span style="color: red;">Please enter a path.</span>';
                return;
            }

            const success = filler.setValue(path, value);
            if (success) {
                document.getElementById('singleResult').innerHTML = `<span style="color: green;">✓ Set ${path} = ${value}</span>`;
            } else {
                document.getElementById('singleResult').innerHTML = `<span style="color: red;">✗ Path not found: ${path}</span>`;
            }
        }

        function setMultipleValues() {
            if (!filler) {
                document.getElementById('multipleResult').innerHTML = '<span style="color: red;">Please load form data first.</span>';
                return;
            }

            try {
                const values = JSON.parse(document.getElementById('multipleValues').value);
                const results = filler.setValues(values);
                
                let resultHtml = '<h4>Results:</h4><ul>';
                for (const [path, success] of Object.entries(results)) {
                    const status = success ? '✓' : '✗';
                    const color = success ? 'green' : 'red';
                    resultHtml += `<li><span style="color: ${color};">${status} ${path}</span></li>`;
                }
                resultHtml += '</ul>';
                
                document.getElementById('multipleResult').innerHTML = resultHtml;
            } catch (error) {
                document.getElementById('multipleResult').innerHTML = '<span style="color: red;">✗ Invalid JSON: ' + error.message + '</span>';
            }
        }

        function getValue() {
            if (!filler) {
                document.getElementById('getResult').innerHTML = '<span style="color: red;">Please load form data first.</span>';
                return;
            }

            const path = document.getElementById('getPath').value;
            if (!path) {
                document.getElementById('getResult').innerHTML = '<span style="color: red;">Please enter a path.</span>';
                return;
            }

            const value = filler.getValue(path);
            if (value !== null) {
                document.getElementById('getResult').innerHTML = `<span style="color: green;">Value: ${value}</span>`;
            } else {
                document.getElementById('getResult').innerHTML = `<span style="color: red;">Path not found: ${path}</span>`;
            }
        }

        function showAllPaths() {
            if (!filler) {
                document.getElementById('pathsList').innerHTML = '<span style="color: red;">Please load form data first.</span>';
                return;
            }

            const paths = filler.getAllPaths();
            let html = `<h4>All Available Paths (${paths.length} total):</h4>`;
            
            paths.forEach(item => {
                html += `<div><strong>${item.path}</strong> → ${item.value} <em>(${item.label})</em></div>`;
            });

            document.getElementById('pathsList').innerHTML = html;
        }

        function downloadUpdatedForm() {
            if (!formData) {
                alert('Please load form data first.');
                return;
            }

            const dataStr = JSON.stringify(formData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = 'form_updated.json';
            link.click();
            
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
