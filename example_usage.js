// Example usage of FormFiller with your form.json
const fs = require('fs');
const FormFiller = require('./form_filler.js');

// Load the form data
const formData = JSON.parse(fs.readFileSync('form.json', 'utf8'));

// Create form filler instance
const filler = new FormFiller(formData);

// Example 1: Set individual values
console.log('Setting individual values...');
filler.setValue('In-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmen', 85);
filler.setValue('In-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmenPartTime', 0);
filler.setValue('In-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmenFTE', 85);

// Example 2: Set multiple values at once
console.log('\nSetting multiple values...');
const valuesToSet = {
  'In-State:Undergrad:Men:Other Freshmen:otherFreshmen': 40,
  'In-State:Undergrad:Men:Other Freshmen:otherFreshmenPartTime': 3,
  'In-State:Undergrad:Men:Other Freshmen:otherFreshmenFTE': 41.5,
  'In-State:Undergrad:Men:Sophomore:sophomore': 65,
  'In-State:Undergrad:Men:Sophomore:sophomorePartTime': 10,
  'In-State:Undergrad:Men:Sophomore:sophomoreFTE': 70.25,
  'In-State:Undergrad:Women:First-Time Freshmen:firstTimeFreshmen': 82,
  'In-State:Undergrad:Women:First-Time Freshmen:firstTimeFreshmenPartTime': 0,
  'In-State:Undergrad:Women:First-Time Freshmen:firstTimeFreshmenFTE': 82,
  'Out-of-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmenOutOfState': 25,
  'Out-of-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmenPartTimeOutOfState': 2,
  'Totals:New Admissions:First-time:firstTimeFreshmenFirstTime': 50,
  'Totals:New Admissions:First-time:medicineFirstTime': 10,
  'Totals:Grand Totals:Head Count:firstTimeFreshmenHeadCount': 107,
  'Totals:Grand Totals:FTE:firstTimeFreshmenFTE': 107
};

const results = filler.setValues(valuesToSet);
console.log('Results:', results);

// Example 3: Get values
console.log('\nGetting values...');
console.log('In-State Men First-Time Freshmen:', 
  filler.getValue('In-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmen'));
console.log('Out-of-State Men First-Time Freshmen:', 
  filler.getValue('Out-of-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmenOutOfState'));

// Example 4: Show first 10 available paths
console.log('\nFirst 10 available paths:');
const allPaths = filler.getAllPaths();
allPaths.slice(0, 10).forEach(item => {
  console.log(`${item.path} -> ${item.value} (${item.label})`);
});

console.log(`\nTotal available paths: ${allPaths.length}`);

// Save the updated form data back to file
fs.writeFileSync('form_updated.json', JSON.stringify(formData, null, 2));
console.log('\nUpdated form data saved to form_updated.json');

// Example 5: Find all paths containing "firstTimeFreshmen"
console.log('\nPaths containing "firstTimeFreshmen":');
const freshmenPaths = allPaths.filter(item => item.key.includes('firstTimeFreshmen'));
freshmenPaths.forEach(item => {
  console.log(`${item.path} -> ${item.value}`);
});
