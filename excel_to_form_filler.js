// Excel to Form Filler - Reads Excel data and fills form.json using simple_mapping.json
const fs = require('fs');
const XLSX = require('xlsx');
const FormFiller = require('./form_filler.js');

class ExcelToFormFiller {
  constructor(mappingPath = 'simple_mapping.json', formPath = 'form.json', excelPath = 'A1.1 Workbook.xlsx') {
    this.mappingPath = mappingPath;
    this.formPath = formPath;
    this.excelPath = excelPath;
    
    // Load mapping and form data
    this.mapping = JSON.parse(fs.readFileSync(mappingPath, 'utf8'));
    this.formData = JSON.parse(fs.readFileSync(formPath, 'utf8'));
    this.filler = new FormFiller(this.formData);
    
    // Load Excel workbook
    this.workbook = XLSX.readFile(excelPath);
    this.worksheet = null;
    
    console.log(`Loaded ${this.mapping.length} mappings from ${mappingPath}`);
    console.log(`Loaded form data from ${formPath}`);
    console.log(`Loaded Excel workbook: ${excelPath}`);
    console.log(`Available worksheets: ${this.workbook.SheetNames.join(', ')}`);
  }

  // Set the active worksheet
  setWorksheet(sheetName) {
    if (this.workbook.SheetNames.includes(sheetName)) {
      this.worksheet = this.workbook.Sheets[sheetName];
      console.log(`Active worksheet set to: ${sheetName}`);
      return true;
    } else {
      console.error(`Worksheet '${sheetName}' not found. Available: ${this.workbook.SheetNames.join(', ')}`);
      return false;
    }
  }

  // Get value from Excel cell
  getCellValue(cellAddress) {
    if (!this.worksheet) {
      throw new Error('No worksheet selected. Call setWorksheet() first.');
    }

    const cell = this.worksheet[cellAddress];
    if (!cell) {
      return null; // Cell is empty or doesn't exist
    }

    // Return the cell value, handling different types
    return cell.v; // .v contains the actual value
  }

  // Convert value based on type
  convertValue(value, type) {
    if (value === null || value === undefined || value === '') {
      return type === 'number' ? 0 : '';
    }

    switch (type) {
      case 'number':
        const num = parseFloat(value);
        return isNaN(num) ? 0 : num;
      case 'text':
      case 'tel':
        return String(value);
      default:
        return value;
    }
  }

  // Fill form with Excel data
  fillFormFromExcel(sheetName = null) {
    // Set worksheet if provided
    if (sheetName && !this.setWorksheet(sheetName)) {
      return { success: false, error: `Could not set worksheet: ${sheetName}` };
    }

    if (!this.worksheet) {
      // Try to use the first worksheet if none is set
      if (this.workbook.SheetNames.length > 0) {
        this.setWorksheet(this.workbook.SheetNames[0]);
      } else {
        return { success: false, error: 'No worksheets available' };
      }
    }

    let successCount = 0;
    let errorCount = 0;
    const errors = [];
    const updates = [];

    // Process each mapping
    this.mapping.forEach(mapping => {
      try {
        // Get value from Excel
        const rawValue = this.getCellValue(mapping.cell);
        
        // Convert value based on type
        const convertedValue = this.convertValue(rawValue, mapping.type);
        
        // Set value in form
        const success = this.filler.setValue(mapping.key, convertedValue);
        
        if (success) {
          successCount++;
          updates.push({
            key: mapping.key,
            cell: mapping.cell,
            value: convertedValue,
            type: mapping.type,
            rawValue: rawValue
          });
        } else {
          errorCount++;
          errors.push(`Failed to set ${mapping.key} from ${mapping.cell}`);
        }
      } catch (error) {
        errorCount++;
        errors.push(`Error processing ${mapping.key} from ${mapping.cell}: ${error.message}`);
      }
    });

    return {
      success: true,
      successCount,
      errorCount,
      errors,
      updates: updates.slice(0, 10), // Show first 10 updates as sample
      totalUpdates: updates.length
    };
  }

  // Save the updated form data
  saveUpdatedForm(outputPath = 'form_filled_from_excel.json') {
    fs.writeFileSync(outputPath, JSON.stringify(this.formData, null, 2));
    console.log(`Updated form saved to: ${outputPath}`);
    return outputPath;
  }

  // Get summary of Excel data for specific cells
  getExcelSummary(sampleCells = ['B7', 'C7', 'D7', 'E7', 'F7']) {
    if (!this.worksheet) {
      return { error: 'No worksheet selected' };
    }

    const summary = {
      worksheet: this.worksheet,
      sampleData: {}
    };

    sampleCells.forEach(cell => {
      const value = this.getCellValue(cell);
      summary.sampleData[cell] = value;
    });

    return summary;
  }

  // Validate mappings against Excel sheet
  validateMappings() {
    if (!this.worksheet) {
      return { error: 'No worksheet selected' };
    }

    const validation = {
      totalMappings: this.mapping.length,
      emptyCells: 0,
      populatedCells: 0,
      invalidCells: 0,
      cellSamples: []
    };

    this.mapping.slice(0, 20).forEach(mapping => { // Check first 20 for sample
      const value = this.getCellValue(mapping.cell);
      const sample = {
        key: mapping.key,
        cell: mapping.cell,
        type: mapping.type,
        value: value,
        status: value === null ? 'empty' : 'populated'
      };

      validation.cellSamples.push(sample);
      
      if (value === null) {
        validation.emptyCells++;
      } else {
        validation.populatedCells++;
      }
    });

    return validation;
  }
}

// Main execution function
function main() {
  try {
    console.log('=== Excel to Form Filler ===\n');
    
    // Create filler instance
    const filler = new ExcelToFormFiller();
    
    // Show Excel summary
    console.log('Excel Summary:');
    const summary = filler.getExcelSummary();
    if (summary.error) {
      console.log(`Error: ${summary.error}`);
    } else {
      console.log('Sample cell values:');
      Object.entries(summary.sampleData).forEach(([cell, value]) => {
        console.log(`  ${cell}: ${value}`);
      });
    }
    
    console.log('\n--- Validation ---');
    const validation = filler.validateMappings();
    if (validation.error) {
      console.log(`Error: ${validation.error}`);
    } else {
      console.log(`Total mappings: ${validation.totalMappings}`);
      console.log(`Sample check - Populated: ${validation.populatedCells}, Empty: ${validation.emptyCells}`);
      
      console.log('\nFirst 5 cell samples:');
      validation.cellSamples.slice(0, 5).forEach(sample => {
        console.log(`  ${sample.cell} (${sample.type}): ${sample.value} [${sample.status}]`);
      });
    }
    
    console.log('\n--- Filling Form ---');
    // Fill form with Excel data
    const result = filler.fillFormFromExcel();
    
    if (result.success) {
      console.log(`✓ Successfully updated ${result.successCount} fields`);
      console.log(`✗ ${result.errorCount} errors`);
      
      if (result.errors.length > 0) {
        console.log('\nFirst 5 errors:');
        result.errors.slice(0, 5).forEach(error => console.log(`  ${error}`));
      }
      
      console.log('\nSample updates:');
      result.updates.forEach(update => {
        console.log(`  ${update.key} = ${update.value} (from ${update.cell})`);
      });
      
      // Save updated form
      const outputPath = filler.saveUpdatedForm();
      console.log(`\n✓ Form data updated and saved to: ${outputPath}`);
      
    } else {
      console.log(`✗ Failed: ${result.error}`);
    }
    
  } catch (error) {
    console.error('Error:', error.message);
    console.log('\nMake sure you have the required files:');
    console.log('- simple_mapping.json');
    console.log('- form.json');
    console.log('- A1.1 Workbook.xlsx');
    console.log('\nAnd install required dependencies:');
    console.log('npm install xlsx');
  }
}

// Export for use as module
module.exports = ExcelToFormFiller;

// Run if executed directly
if (require.main === module) {
  main();
}
