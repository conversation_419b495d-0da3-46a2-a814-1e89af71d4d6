// <PERSON>ript to add type field to all mappings in simple_mapping.json
const fs = require('fs');
const FormFiller = require('./form_filler.js');

// Load the form data and mapping
const formData = JSON.parse(fs.readFileSync('form.json', 'utf8'));
const mapping = JSON.parse(fs.readFileSync('simple_mapping.json', 'utf8'));

// Create form filler instance to get type information
const filler = new FormFiller(formData);

// Get all paths with their type information
const allPaths = filler.getAllPaths();

// Create a lookup map for types by path
const typeMap = {};
allPaths.forEach(item => {
  typeMap[item.path] = item.type;
});

// Add type field to each mapping entry
const updatedMapping = mapping.map(entry => {
  const type = typeMap[entry.key];
  return {
    key: entry.key,
    cell: entry.cell,
    type: type || 'unknown' // fallback if type not found
  };
});

// Save the updated mapping
fs.writeFileSync('simple_mapping_with_types.json', JSON.stringify(updatedMapping, null, 2));

console.log(`Updated ${updatedMapping.length} mapping entries with type information`);

// Show statistics
const typeStats = {};
updatedMapping.forEach(entry => {
  typeStats[entry.type] = (typeStats[entry.type] || 0) + 1;
});

console.log('\nType distribution:');
Object.entries(typeStats).forEach(([type, count]) => {
  console.log(`  ${type}: ${count} entries`);
});

// Show first few entries as example
console.log('\nFirst 5 entries with types:');
updatedMapping.slice(0, 5).forEach(entry => {
  console.log(`  ${entry.key} -> ${entry.cell} (${entry.type})`);
});

// Check for any unknown types
const unknownEntries = updatedMapping.filter(entry => entry.type === 'unknown');
if (unknownEntries.length > 0) {
  console.log(`\nWarning: ${unknownEntries.length} entries have unknown type:`);
  unknownEntries.slice(0, 5).forEach(entry => {
    console.log(`  ${entry.key}`);
  });
}
