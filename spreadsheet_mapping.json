{"metadata": {"description": "Mapping between form.json keys and spreadsheet cell locations", "created": "2025-07-01T11:57:47.798Z", "totalFields": 377}, "mappings": {"firstTimeFreshmen": {"path": "In-State:Undergrad:Women:First-Time Freshmen:firstTimeFreshmen", "label": "First-Time Freshmen", "type": "number", "currentValue": 0, "spreadsheetCell": "A23", "section": "In-State", "comments": ""}, "firstTimeFreshmenPartTime": {"path": "In-State:Undergrad:Women:First-Time Freshmen:firstTimeFreshmenPartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A24", "section": "In-State", "comments": ""}, "firstTimeFreshmenFTE": {"path": "Totals:Grand Totals:FTE:firstTimeFreshmenFTE", "label": "First-Time Freshmen", "type": "number", "currentValue": 0, "spreadsheetCell": "A348", "section": "Totals", "comments": ""}, "otherFreshmen": {"path": "In-State:Undergrad:Women:Other Freshmen:otherFreshmen", "label": "Other Freshmen", "type": "number", "currentValue": 0, "spreadsheetCell": "A26", "section": "In-State", "comments": ""}, "otherFreshmenPartTime": {"path": "In-State:Undergrad:Women:Other Freshmen:otherFreshmenPartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A27", "section": "In-State", "comments": ""}, "otherFreshmenFTE": {"path": "Totals:Grand Totals:FTE:otherFreshmenFTE", "label": "Other Freshmen", "type": "number", "currentValue": 0, "spreadsheetCell": "A349", "section": "Totals", "comments": ""}, "sophomore": {"path": "In-State:Undergrad:Women:Sophomore:sophomore", "label": "Sophomore", "type": "number", "currentValue": 0, "spreadsheetCell": "A29", "section": "In-State", "comments": ""}, "sophomorePartTime": {"path": "In-State:Undergrad:Women:Sophomore:sophomorePartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A30", "section": "In-State", "comments": ""}, "sophomoreFTE": {"path": "Totals:Grand Totals:FTE:sophomoreFTE", "label": "Sophomore", "type": "number", "currentValue": 0, "spreadsheetCell": "A350", "section": "Totals", "comments": ""}, "junior": {"path": "In-State:Undergrad:Women:Junior:junior", "label": "Junior", "type": "number", "currentValue": 0, "spreadsheetCell": "A32", "section": "In-State", "comments": ""}, "juniorPartTime": {"path": "In-State:Undergrad:Women:Junior:juniorPartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A33", "section": "In-State", "comments": ""}, "juniorFTE": {"path": "Totals:Grand Totals:FTE:juniorFTE", "label": "Junior", "type": "number", "currentValue": 0, "spreadsheetCell": "A351", "section": "Totals", "comments": ""}, "senior": {"path": "In-State:Undergrad:Women:Senior:senior", "label": "Senior", "type": "number", "currentValue": 0, "spreadsheetCell": "A35", "section": "In-State", "comments": ""}, "seniorPartTime": {"path": "In-State:Undergrad:Women:Senior:seniorPartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A36", "section": "In-State", "comments": ""}, "seniorFTE": {"path": "In-State:Undergrad:Women:Senior:seniorFTE", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A37", "section": "In-State", "comments": ""}, "specialUnclass": {"path": "In-State:Undergrad:Women:Special/Unclassified:specialUnclass", "label": "Special/Unclassified", "type": "number", "currentValue": 0, "spreadsheetCell": "A38", "section": "In-State", "comments": ""}, "specialUnclassPartTime": {"path": "In-State:Undergrad:Women:Special/Unclassified:specialUnclassPartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A39", "section": "In-State", "comments": ""}, "specialUnclassFTE": {"path": "In-State:Undergrad:Women:Special/Unclassified:specialUnclassFTE", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A40", "section": "In-State", "comments": ""}, "undergradTotal": {"path": "In-State:Undergrad:Women:Undergrad Total:undergradTotal", "label": "Undergrad Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A41", "section": "In-State", "comments": ""}, "undergradTotalPartTime": {"path": "In-State:Undergrad:Women:Undergrad Total:undergradTotalPartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A42", "section": "In-State", "comments": ""}, "undergradTotalFTE": {"path": "Totals:Grand Totals:FTE:undergradTotalFTE", "label": "Undergrad Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A354", "section": "Totals", "comments": ""}, "medicine": {"path": "In-State:DocProf:Women:Medicine:medicine", "label": "Medicine", "type": "number", "currentValue": 0, "spreadsheetCell": "A68", "section": "In-State", "comments": ""}, "medicinePartTime": {"path": "In-State:DocProf:Women:Medicine:medicinePartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A69", "section": "In-State", "comments": ""}, "medicineFTE": {"path": "Totals:Grand Totals:FTE:medicineFTE", "label": "Medicine", "type": "number", "currentValue": 0, "spreadsheetCell": "A355", "section": "Totals", "comments": ""}, "pharmacy": {"path": "In-State:DocProf:Women:Pharmacy:pharmacy", "label": "Pharmacy", "type": "number", "currentValue": 0, "spreadsheetCell": "A71", "section": "In-State", "comments": ""}, "pharmacyPartTime": {"path": "In-State:DocProf:Women:Pharmacy:pharmacyPartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A72", "section": "In-State", "comments": ""}, "pharmacyFTE": {"path": "Totals:Grand Totals:FTE:pharmacyFTE", "label": "Pharmacy", "type": "number", "currentValue": 0, "spreadsheetCell": "A356", "section": "Totals", "comments": ""}, "law": {"path": "In-State:Doc<PERSON>rof:Women:Law:law", "label": "Law", "type": "number", "currentValue": 0, "spreadsheetCell": "A74", "section": "In-State", "comments": ""}, "lawPartTime": {"path": "In-State:DocProf:Women:Law:lawPartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A75", "section": "In-State", "comments": ""}, "lawFTE": {"path": "Totals:Grand Totals:FTE:lawFTE", "label": "Law", "type": "number", "currentValue": 0, "spreadsheetCell": "A357", "section": "Totals", "comments": ""}, "theology": {"path": "In-State:DocProf:Women:Theology:theology", "label": "Theology", "type": "number", "currentValue": 0, "spreadsheetCell": "A77", "section": "In-State", "comments": ""}, "theologyPartTime": {"path": "In-State:DocProf:Women:Theology:theologyPartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A78", "section": "In-State", "comments": ""}, "theologyFTE": {"path": "Totals:Grand Totals:FTE:theologyFTE", "label": "Theology", "type": "number", "currentValue": 0, "spreadsheetCell": "A358", "section": "Totals", "comments": ""}, "physicalTherapy": {"path": "In-State:DocProf:Women:Physical Therapy:physicalTherapy", "label": "Physical Therapy", "type": "number", "currentValue": 0, "spreadsheetCell": "A80", "section": "In-State", "comments": ""}, "physicalTherapyPartTime": {"path": "In-State:DocProf:Women:Physical Therapy:physicalTherapyPartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A81", "section": "In-State", "comments": ""}, "physicalTherapyFTE": {"path": "Totals:Grand Totals:FTE:physicalTherapyFTE", "label": "Physical Therapy", "type": "number", "currentValue": 0, "spreadsheetCell": "A359", "section": "Totals", "comments": ""}, "nursePractition": {"path": "In-State:DocProf:Women:Nurse Practition:nursePractition", "label": "Nurse Practition", "type": "number", "currentValue": 0, "spreadsheetCell": "A83", "section": "In-State", "comments": ""}, "nursePractitionPartTime": {"path": "In-State:DocProf:Women:Nurse Practition:nursePractitionPartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A84", "section": "In-State", "comments": ""}, "nursePractitionFTE": {"path": "Totals:Grand Totals:FTE:nursePractitionFTE", "label": "Nurse Practition", "type": "number", "currentValue": 0, "spreadsheetCell": "A360", "section": "Totals", "comments": ""}, "osteopathicMedicine": {"path": "In-State:DocProf:Women:Osteopathic Medicine:osteopathicMedicine", "label": "Osteopathic Medicine", "type": "number", "currentValue": 0, "spreadsheetCell": "A86", "section": "In-State", "comments": ""}, "osteopathicMedicinePartTime": {"path": "In-State:DocProf:Women:Osteopathic Medicine:osteopathicMedicinePartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A87", "section": "In-State", "comments": ""}, "osteopathicMedicineFTE": {"path": "Totals:Grand Totals:FTE:osteopathicMedicineFTE", "label": "Osteopathic Medicine", "type": "number", "currentValue": 0, "spreadsheetCell": "A361", "section": "Totals", "comments": ""}, "docProfTotal": {"path": "In-State:DocProf:Women:Doc. Prof. Total:docProfTotal", "label": "Doc. Prof. Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A89", "section": "In-State", "comments": ""}, "docProfTotalPartTime": {"path": "In-State:DocProf:Women:Doc. Prof. Total:docProfTotalPartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A90", "section": "In-State", "comments": ""}, "docProfTotalFTE": {"path": "Totals:Grand Totals:FTE:docProfTotalFTE", "label": "Doc. Prof. Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A362", "section": "Totals", "comments": ""}, "masters": {"path": "In-State:MastDocRes:Women:Master's:masters", "label": "Master's", "type": "number", "currentValue": 0, "spreadsheetCell": "A104", "section": "In-State", "comments": ""}, "mastersPartTime": {"path": "In-State:MastDocRes:Women:Master's:mastersPartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A105", "section": "In-State", "comments": ""}, "mastersFTE": {"path": "Totals:Grand Totals:FTE:mastersFTE", "label": "Master's", "type": "number", "currentValue": 0, "spreadsheetCell": "A363", "section": "Totals", "comments": ""}, "doctoralResearch": {"path": "In-State:MastDocRes:Women:Doctoral Research:doctoralResearch", "label": "Doctoral Research", "type": "number", "currentValue": 0, "spreadsheetCell": "A107", "section": "In-State", "comments": ""}, "doctoralResearchPartTime": {"path": "In-State:MastDocRes:Women:Doctoral Research:doctoralResearchPartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A108", "section": "In-State", "comments": ""}, "doctoralResearchFTE": {"path": "Totals:Grand Totals:FTE:doctoralResearchFTE", "label": "Doctoral Research", "type": "number", "currentValue": 0, "spreadsheetCell": "A364", "section": "Totals", "comments": ""}, "specialUnclassified": {"path": "In-State:MastDocRes:Women:Spec./Unclass.:specialUnclassified", "label": "Spec./Unclass.", "type": "number", "currentValue": 0, "spreadsheetCell": "A110", "section": "In-State", "comments": ""}, "specialUnclassifiedPartTime": {"path": "In-State:MastDocRes:Women:Spec./Unclass.:specialUnclassifiedPartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A111", "section": "In-State", "comments": ""}, "specialUnclassifiedFTE": {"path": "In-State:MastDocRes:Women:Spec./Unclass.:specialUnclassifiedFTE", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A112", "section": "In-State", "comments": ""}, "mastDocResTotal": {"path": "In-State:MastDocRes:Women:Mast. & Doc. Res. Total:mastDocResTotal", "label": "Mast. & Doc. Res. Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A113", "section": "In-State", "comments": ""}, "mastDocResTotalPartTime": {"path": "In-State:MastDocRes:Women:Mast. & Doc. Res. Total:mastDocResTotalPartTime", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A114", "section": "In-State", "comments": ""}, "mastDocResTotalFTE": {"path": "Totals:Grand Totals:FTE:mastDocResTotalFTE", "label": "Mast.& Doc. Res. Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A366", "section": "Totals", "comments": ""}, "fullTimeTotal": {"path": "In-State:GrandTotals:Women:fullTimeTotal", "label": "Full Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A119", "section": "In-State", "comments": ""}, "partTimeTotal": {"path": "In-State:GrandTotals:Women:partTimeTotal", "label": "Part Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A120", "section": "In-State", "comments": ""}, "totalFTETotal": {"path": "In-State:GrandTotals:Women:totalFTETotal", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A121", "section": "In-State", "comments": ""}, "grandTotalCombinedFullTime": {"path": "In-State:GrandTotals:Total:grandTotalCombinedFullTime", "label": "Grand Total - Full Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A122", "section": "In-State", "comments": ""}, "grandTotalCombinedPartTime": {"path": "In-State:GrandTotals:Total:grandTotalCombinedPartTime", "label": "Grand Total - Part Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A123", "section": "In-State", "comments": ""}, "grandTotalCombinedFTE": {"path": "In-State:GrandTotals:Total:grandTotalCombinedFTE", "label": "Grand Total - Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A124", "section": "In-State", "comments": ""}, "firstTimeFreshmenOutOfState": {"path": "Out-of-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmenOutOfState", "label": "First-Time Freshmen", "type": "number", "currentValue": 0, "spreadsheetCell": "A125", "section": "Out-of-State", "comments": ""}, "firstTimeFreshmenPartTimeOutOfState": {"path": "Out-of-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmenPartTimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A126", "section": "Out-of-State", "comments": ""}, "firstTimeFreshmenFTEOutOfState": {"path": "Out-of-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmenFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A127", "section": "Out-of-State", "comments": ""}, "otherFreshmenOutOfState": {"path": "Out-of-State:Undergrad:Men:Other Freshmen:otherFreshmenOutOfState", "label": "Other Freshmen", "type": "number", "currentValue": 0, "spreadsheetCell": "A128", "section": "Out-of-State", "comments": ""}, "otherFreshmenPartTimeOutOfState": {"path": "Out-of-State:Undergrad:Men:Other Freshmen:otherFreshmenPartTimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A129", "section": "Out-of-State", "comments": ""}, "otherFreshmenFTEOutOfState": {"path": "Out-of-State:Undergrad:Men:Other Freshmen:otherFreshmenFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A130", "section": "Out-of-State", "comments": ""}, "sophomoreOutOfState": {"path": "Out-of-State:Undergrad:Men:Sophomore:sophomoreOutOfState", "label": "Sophomore", "type": "number", "currentValue": 0, "spreadsheetCell": "A131", "section": "Out-of-State", "comments": ""}, "sophomorePartTimeOutOfState": {"path": "Out-of-State:Undergrad:Men:Sophomore:sophomore<PERSON><PERSON>TimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A132", "section": "Out-of-State", "comments": ""}, "sophomoreFTEOutOfState": {"path": "Out-of-State:Undergrad:Men:Sophomore:sophomoreFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A133", "section": "Out-of-State", "comments": ""}, "juniorOutOfState": {"path": "Out-of-State:Undergrad:Men:Junior:juniorOutOfState", "label": "Junior", "type": "number", "currentValue": 0, "spreadsheetCell": "A134", "section": "Out-of-State", "comments": ""}, "juniorPartTimeOutOfState": {"path": "Out-of-State:Undergrad:Men:Junior:juniorPartTimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A135", "section": "Out-of-State", "comments": ""}, "juniorFTEOutOfState": {"path": "Out-of-State:Undergrad:Men:Junior:juniorFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A136", "section": "Out-of-State", "comments": ""}, "seniorOutOfState": {"path": "Out-of-State:Undergrad:Men:Senior:seniorOutOfState", "label": "Senior", "type": "number", "currentValue": 0, "spreadsheetCell": "A137", "section": "Out-of-State", "comments": ""}, "seniorPartTimeOutOfState": {"path": "Out-of-State:Undergrad:Men:Senior:seniorPartTimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A138", "section": "Out-of-State", "comments": ""}, "seniorFTEOutOfState": {"path": "Out-of-State:Undergrad:Men:Senior:seniorFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A139", "section": "Out-of-State", "comments": ""}, "specialUnclassOutOfState": {"path": "Out-of-State:Undergrad:Men:Special/Unclassified:specialUnclassOutOfState", "label": "Special/Unclassified", "type": "number", "currentValue": 0, "spreadsheetCell": "A140", "section": "Out-of-State", "comments": ""}, "specialUnclassPartTimeOutOfState": {"path": "Out-of-State:Undergrad:Men:Special/Unclassified:specialUnclassPartTimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A141", "section": "Out-of-State", "comments": ""}, "specialUnclassFTEOutOfState": {"path": "Out-of-State:Undergrad:Men:Special/Unclassified:specialUnclassFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A142", "section": "Out-of-State", "comments": ""}, "undergradTotalOutOfState": {"path": "Out-of-State:Undergrad:Men:Undergrad Total:undergradTotalOutOfState", "label": "Undergrad Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A143", "section": "Out-of-State", "comments": ""}, "undergradTotalPartTimeOutOfState": {"path": "Out-of-State:Undergrad:Men:Undergrad Total:undergradTotalPartTimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A144", "section": "Out-of-State", "comments": ""}, "undergradTotalFTEOutOfState": {"path": "Out-of-State:Undergrad:Men:Undergrad Total:undergradTotalFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A145", "section": "Out-of-State", "comments": ""}, "firstTimeFreshmenWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:First-Time Freshmen:firstTimeFreshmenWomenOutOfState", "label": "First-Time Freshmen", "type": "number", "currentValue": 0, "spreadsheetCell": "A146", "section": "Out-of-State", "comments": ""}, "firstTimeFreshmenPartTimeWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:First-Time Freshmen:firstTimeFreshmenPartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A147", "section": "Out-of-State", "comments": ""}, "firstTimeFreshmenFTEWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:First-Time Freshmen:firstTimeFreshmenFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A148", "section": "Out-of-State", "comments": ""}, "otherFreshmenWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:Other Freshmen:otherFreshmenWomenOutOfState", "label": "Other Freshmen", "type": "number", "currentValue": 0, "spreadsheetCell": "A149", "section": "Out-of-State", "comments": ""}, "otherFreshmenPartTimeWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:Other Freshmen:otherFreshmenPartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A150", "section": "Out-of-State", "comments": ""}, "otherFreshmenFTEWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:Other Freshmen:otherFreshmenFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A151", "section": "Out-of-State", "comments": ""}, "sophomoreWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:Sophomore:sophomore<PERSON><PERSON>nOutOfState", "label": "Sophomore", "type": "number", "currentValue": 0, "spreadsheetCell": "A152", "section": "Out-of-State", "comments": ""}, "sophomorePartTimeWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:Sophomore:sophomorePartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A153", "section": "Out-of-State", "comments": ""}, "sophomoreFTEWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:Sophomore:sophomoreFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A154", "section": "Out-of-State", "comments": ""}, "juniorWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:Junior:juniorWomenOutOfState", "label": "Junior", "type": "number", "currentValue": 0, "spreadsheetCell": "A155", "section": "Out-of-State", "comments": ""}, "juniorPartTimeWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:Junior:juniorPartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A156", "section": "Out-of-State", "comments": ""}, "juniorFTEWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:Junior:juniorFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A157", "section": "Out-of-State", "comments": ""}, "seniorWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:Senior:seniorWomenOutOfState", "label": "Senior", "type": "number", "currentValue": 0, "spreadsheetCell": "A158", "section": "Out-of-State", "comments": ""}, "seniorPartTimeWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:Senior:seniorPartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A159", "section": "Out-of-State", "comments": ""}, "seniorFTEWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:Senior:seniorFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A160", "section": "Out-of-State", "comments": ""}, "specialUnclassWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:Special/Unclassified:specialUnclassWomenOutOfState", "label": "Special/Unclassified", "type": "number", "currentValue": 0, "spreadsheetCell": "A161", "section": "Out-of-State", "comments": ""}, "specialUnclassPartTimeWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:Special/Unclassified:specialUnclassPartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A162", "section": "Out-of-State", "comments": ""}, "specialUnclassFTEWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:Special/Unclassified:specialUnclassFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A163", "section": "Out-of-State", "comments": ""}, "undergradTotalWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:Undergrad Total:undergradTotalWomenOutOfState", "label": "Undergrad Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A164", "section": "Out-of-State", "comments": ""}, "undergradTotalPartTimeWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:Undergrad Total:undergradTotalPartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A165", "section": "Out-of-State", "comments": ""}, "undergradTotalFTEWomenOutOfState": {"path": "Out-of-State:Undergrad:Women:Undergrad Total:undergradTotalFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A166", "section": "Out-of-State", "comments": ""}, "medicineOutOfState": {"path": "Out-of-State:DocProf:Men:Medicine:medicineOutOfState", "label": "Medicine", "type": "number", "currentValue": 0, "spreadsheetCell": "A167", "section": "Out-of-State", "comments": ""}, "medicinePartTimeOutOfState": {"path": "Out-of-State:DocProf:Men:Medicine:medicinePartTimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A168", "section": "Out-of-State", "comments": ""}, "medicineFTEOutOfState": {"path": "Out-of-State:DocProf:Men:Medicine:medicineFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A169", "section": "Out-of-State", "comments": ""}, "pharmacyOutOfState": {"path": "Out-of-State:DocProf:Men:Pharmacy:pharmacyOutOfState", "label": "Pharmacy", "type": "number", "currentValue": 0, "spreadsheetCell": "A170", "section": "Out-of-State", "comments": ""}, "pharmacyPartTimeOutOfState": {"path": "Out-of-State:DocProf:Men:Pharmacy:pharmacyPartTimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A171", "section": "Out-of-State", "comments": ""}, "pharmacyFTEOutOfState": {"path": "Out-of-State:DocProf:Men:Pharmacy:pharmacyFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A172", "section": "Out-of-State", "comments": ""}, "lawOutOfState": {"path": "Out-of-State:DocProf:Men:Law:lawOutOfState", "label": "Law", "type": "number", "currentValue": 0, "spreadsheetCell": "A173", "section": "Out-of-State", "comments": ""}, "lawPartTimeOutOfState": {"path": "Out-of-State:DocProf:Men:Law:lawPartTimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A174", "section": "Out-of-State", "comments": ""}, "lawFTEOutOfState": {"path": "Out-of-State:DocProf:Men:Law:lawFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A175", "section": "Out-of-State", "comments": ""}, "theologyOutOfState": {"path": "Out-of-State:DocProf:Men:Theology:theologyOutOfState", "label": "Theology", "type": "number", "currentValue": 0, "spreadsheetCell": "A176", "section": "Out-of-State", "comments": ""}, "theologyPartTimeOutOfState": {"path": "Out-of-State:DocProf:Men:Theology:theologyPartTimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A177", "section": "Out-of-State", "comments": ""}, "theologyFTEOutOfState": {"path": "Out-of-State:DocProf:Men:Theology:theologyFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A178", "section": "Out-of-State", "comments": ""}, "physicalTherapyOutOfState": {"path": "Out-of-State:DocProf:Men:Physical Therapy:physicalTherapyOutOfState", "label": "Physical Therapy", "type": "number", "currentValue": 0, "spreadsheetCell": "A179", "section": "Out-of-State", "comments": ""}, "physicalTherapyPartTimeOutOfState": {"path": "Out-of-State:DocProf:Men:Physical Therapy:physicalTherapyPartTimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A180", "section": "Out-of-State", "comments": ""}, "physicalTherapyFTEOutOfState": {"path": "Out-of-State:DocProf:Men:Physical Therapy:physicalTherapyFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A181", "section": "Out-of-State", "comments": ""}, "nursePractitionOutOfState": {"path": "Out-of-State:DocProf:Men:Nurse Practition:nursePractitionOutOfState", "label": "Nurse Practition", "type": "number", "currentValue": 0, "spreadsheetCell": "A182", "section": "Out-of-State", "comments": ""}, "nursePractitionPartTimeOutOfState": {"path": "Out-of-State:DocProf:Men:Nurse Practition:nursePractitionPartTimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A183", "section": "Out-of-State", "comments": ""}, "nursePractitionFTEOutOfState": {"path": "Out-of-State:DocProf:Men:Nurse Practition:nursePractitionFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A184", "section": "Out-of-State", "comments": ""}, "osteopathicMedicineOutOfState": {"path": "Out-of-State:DocProf:Men:Osteopathic Medicine:osteopathicMedicineOutOfState", "label": "Osteopathic Medicine", "type": "number", "currentValue": 0, "spreadsheetCell": "A185", "section": "Out-of-State", "comments": ""}, "osteopathicMedicinePartTimeOutOfState": {"path": "Out-of-State:DocProf:Men:Osteopathic Medicine:osteopathicMedicinePartTimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A186", "section": "Out-of-State", "comments": ""}, "osteopathicMedicineFTEOutOfState": {"path": "Out-of-State:DocProf:Men:Osteopathic Medicine:osteopathicMedicineFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A187", "section": "Out-of-State", "comments": ""}, "docProfTotalOutOfState": {"path": "Out-of-State:DocProf:Men:Doc. Prof. Total:docProfTotalOutOfState", "label": "Doc. Prof. Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A188", "section": "Out-of-State", "comments": ""}, "docProfTotalPartTimeOutOfState": {"path": "Out-of-State:DocProf:Men:Doc. Prof. Total:docProfTotalPartTimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A189", "section": "Out-of-State", "comments": ""}, "docProfTotalFTEOutOfState": {"path": "Out-of-State:DocProf:Men:Doc. Prof. Total:docProfTotalFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A190", "section": "Out-of-State", "comments": ""}, "medicineWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Medicine:medicineWomenOutOfState", "label": "Medicine", "type": "number", "currentValue": 0, "spreadsheetCell": "A191", "section": "Out-of-State", "comments": ""}, "medicinePartTimeWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Medicine:medicinePartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A192", "section": "Out-of-State", "comments": ""}, "medicineFTEWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Medicine:medicineFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A193", "section": "Out-of-State", "comments": ""}, "pharmacyWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Pharmacy:pharmacyWomenOutOfState", "label": "Pharmacy", "type": "number", "currentValue": 0, "spreadsheetCell": "A194", "section": "Out-of-State", "comments": ""}, "pharmacyPartTimeWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Pharmacy:pharmacyPartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A195", "section": "Out-of-State", "comments": ""}, "pharmacyFTEWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Pharmacy:pharmacyFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A196", "section": "Out-of-State", "comments": ""}, "lawWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Law:lawWomenOutOfState", "label": "Law", "type": "number", "currentValue": 0, "spreadsheetCell": "A197", "section": "Out-of-State", "comments": ""}, "lawPartTimeWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Law:lawPartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A198", "section": "Out-of-State", "comments": ""}, "lawFTEWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Law:lawFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A199", "section": "Out-of-State", "comments": ""}, "theologyWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Theology:theologyWomenOutOfState", "label": "Theology", "type": "number", "currentValue": 0, "spreadsheetCell": "A200", "section": "Out-of-State", "comments": ""}, "theologyPartTimeWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Theology:theologyPartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A201", "section": "Out-of-State", "comments": ""}, "theologyFTEWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Theology:theologyFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A202", "section": "Out-of-State", "comments": ""}, "physicalTherapyWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Physical Therapy:physicalTherapyWomenOutOfState", "label": "Physical Therapy", "type": "number", "currentValue": 0, "spreadsheetCell": "A203", "section": "Out-of-State", "comments": ""}, "physicalTherapyPartTimeWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Physical Therapy:physicalTherapyPartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A204", "section": "Out-of-State", "comments": ""}, "physicalTherapyFTEWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Physical Therapy:physicalTherapyFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A205", "section": "Out-of-State", "comments": ""}, "nursePractitionWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Nurse Practition:nursePractitionWomenOutOfState", "label": "Nurse Practition", "type": "number", "currentValue": 0, "spreadsheetCell": "A206", "section": "Out-of-State", "comments": ""}, "nursePractitionPartTimeWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Nurse Practition:nursePractitionPartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A207", "section": "Out-of-State", "comments": ""}, "nursePractitionFTEWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Nurse Practition:nursePractitionFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A208", "section": "Out-of-State", "comments": ""}, "osteopathicMedicineWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Osteopathic Medicine:osteopathicMedicineWomenOutOfState", "label": "Osteopathic Medicine", "type": "number", "currentValue": 0, "spreadsheetCell": "A209", "section": "Out-of-State", "comments": ""}, "osteopathicMedicinePartTimeWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Osteopathic Medicine:osteopathicMedicinePartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A210", "section": "Out-of-State", "comments": ""}, "osteopathicMedicineFTEWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Osteopathic Medicine:osteopathicMedicineFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A211", "section": "Out-of-State", "comments": ""}, "docProfTotalWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Doc. Prof. Total:docProfTotalWomenOutOfState", "label": "Doc. Prof. Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A212", "section": "Out-of-State", "comments": ""}, "docProfTotalPartTimeWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Doc. Prof. Total:docProfTotalPartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A213", "section": "Out-of-State", "comments": ""}, "docProfTotalFTEWomenOutOfState": {"path": "Out-of-State:DocProf:Women:Doc. Prof. Total:docProfTotalFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A214", "section": "Out-of-State", "comments": ""}, "mastersOutOfState": {"path": "Out-of-State:MastDocRes:Men:Master's:mastersOutOfState", "label": "Master's", "type": "number", "currentValue": 0, "spreadsheetCell": "A215", "section": "Out-of-State", "comments": ""}, "mastersPartTimeOutOfState": {"path": "Out-of-State:MastDocRes:Men:Master's:mastersPartTimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A216", "section": "Out-of-State", "comments": ""}, "mastersFTEOutOfState": {"path": "Out-of-State:MastDocRes:Men:Master's:mastersFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A217", "section": "Out-of-State", "comments": ""}, "doctoralResearchOutOfState": {"path": "Out-of-State:MastDocRes:Men:Doctoral Research:doctoralResearchOutOfState", "label": "Doctoral Research", "type": "number", "currentValue": 0, "spreadsheetCell": "A218", "section": "Out-of-State", "comments": ""}, "doctoralResearchPartTimeOutOfState": {"path": "Out-of-State:MastDocRes:Men:Doctoral Research:doctoralResearchPartTimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A219", "section": "Out-of-State", "comments": ""}, "doctoralResearchFTEOutOfState": {"path": "Out-of-State:MastDocRes:Men:Doctoral Research:doctoralResearchFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A220", "section": "Out-of-State", "comments": ""}, "specialUnclassifiedOutOfState": {"path": "Out-of-State:MastDocRes:Men:Spec./Unclass.:specialUnclassifiedOutOfState", "label": "Spec./Unclass.", "type": "number", "currentValue": 0, "spreadsheetCell": "A221", "section": "Out-of-State", "comments": ""}, "specialUnclassifiedPartTimeOutOfState": {"path": "Out-of-State:MastDocRes:Men:Spec./Unclass.:specialUnclassifiedPartTimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A222", "section": "Out-of-State", "comments": ""}, "specialUnclassifiedFTEOutOfState": {"path": "Out-of-State:MastDocRes:Men:Spec./Unclass.:specialUnclassifiedFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A223", "section": "Out-of-State", "comments": ""}, "mastDocResTotalOutOfState": {"path": "Out-of-State:MastDocRes:Men:Mast. & Doc. Res. Total:mastDocResTotalOutOfState", "label": "Mast. & Doc. Res. Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A224", "section": "Out-of-State", "comments": ""}, "mastDocResTotalPartTimeOutOfState": {"path": "Out-of-State:MastDocRes:Men:Mast. & Doc. Res. Total:mastDocResTotalPartTimeOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A225", "section": "Out-of-State", "comments": ""}, "mastDocResTotalFTEOutOfState": {"path": "Out-of-State:MastDocRes:Men:Mast. & Doc. Res. Total:mastDocResTotalFTEOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A226", "section": "Out-of-State", "comments": ""}, "mastersWomenOutOfState": {"path": "Out-of-State:MastDocRes:Women:Master's:mastersWomenOutOfState", "label": "Master's", "type": "number", "currentValue": 0, "spreadsheetCell": "A227", "section": "Out-of-State", "comments": ""}, "mastersPartTimeWomenOutOfState": {"path": "Out-of-State:MastDocRes:Women:Master's:mastersPartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A228", "section": "Out-of-State", "comments": ""}, "mastersFTEWomenOutOfState": {"path": "Out-of-State:MastDocRes:Women:Master's:mastersFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A229", "section": "Out-of-State", "comments": ""}, "doctoralResearchWomenOutOfState": {"path": "Out-of-State:MastDocRes:Women:Doctoral Research:doctoralResearchWomenOutOfState", "label": "Doctoral Research", "type": "number", "currentValue": 0, "spreadsheetCell": "A230", "section": "Out-of-State", "comments": ""}, "doctoralResearchPartTimeWomenOutOfState": {"path": "Out-of-State:MastDocRes:Women:Doctoral Research:doctoralResearchPartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A231", "section": "Out-of-State", "comments": ""}, "doctoralResearchFTEWomenOutOfState": {"path": "Out-of-State:MastDocRes:Women:Doctoral Research:doctoralResearchFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A232", "section": "Out-of-State", "comments": ""}, "specialUnclassifiedWomenOutOfState": {"path": "Out-of-State:MastDocRes:Women:Spec./Unclass.:specialUnclassifiedWomenOutOfState", "label": "Spec./Unclass.", "type": "number", "currentValue": 0, "spreadsheetCell": "A233", "section": "Out-of-State", "comments": ""}, "specialUnclassifiedPartTimeWomenOutOfState": {"path": "Out-of-State:MastDocRes:Women:Spec./Unclass.:specialUnclassifiedPartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A234", "section": "Out-of-State", "comments": ""}, "specialUnclassifiedFTEWomenOutOfState": {"path": "Out-of-State:MastDocRes:Women:Spec./Unclass.:specialUnclassifiedFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A235", "section": "Out-of-State", "comments": ""}, "mastDocResTotalWomenOutOfState": {"path": "Out-of-State:MastDocRes:Women:Mast. & Doc. Res. Total:mastDocResTotalWomenOutOfState", "label": "Mast. & Doc. Res. Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A236", "section": "Out-of-State", "comments": ""}, "mastDocResTotalPartTimeWomenOutOfState": {"path": "Out-of-State:MastDocRes:Women:Mast. & Doc. Res. Total:mastDocResTotalPartTimeWomenOutOfState", "label": "Part-Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A237", "section": "Out-of-State", "comments": ""}, "mastDocResTotalFTEWomenOutOfState": {"path": "Out-of-State:MastDocRes:Women:Mast. & Doc. Res. Total:mastDocResTotalFTEWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A238", "section": "Out-of-State", "comments": ""}, "fullTimeTotalOutOfState": {"path": "Out-of-State:GrandTotals:Men:fullTimeTotalOutOfState", "label": "Full Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A239", "section": "Out-of-State", "comments": ""}, "partTimeTotalOutOfState": {"path": "Out-of-State:GrandTotals:Men:partTimeTotalOutOfState", "label": "Grand Total - Part Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A240", "section": "Out-of-State", "comments": ""}, "totalFTETotalOutOfState": {"path": "Out-of-State:GrandTotals:Men:totalFTETotalOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A241", "section": "Out-of-State", "comments": ""}, "fullTimeTotalWomenOutOfState": {"path": "Out-of-State:GrandTotals:Women:fullTimeTotalWomenOutOfState", "label": "Full Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A242", "section": "Out-of-State", "comments": ""}, "partTimeTotalWomenOutOfState": {"path": "Out-of-State:GrandTotals:Women:partTimeTotalWomenOutOfState", "label": "Part Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A243", "section": "Out-of-State", "comments": ""}, "totalFTETotalWomenOutOfState": {"path": "Out-of-State:GrandTotals:Women:totalFTETotalWomenOutOfState", "label": "Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A244", "section": "Out-of-State", "comments": ""}, "grandTotalCombinedFullTimeOutOfState": {"path": "Out-of-State:GrandTotals:Total:grandTotalCombinedFullTimeOutOfState", "label": "Grand Total - Full Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A245", "section": "Out-of-State", "comments": ""}, "grandTotalCombinedPartTimeOutOfState": {"path": "Out-of-State:GrandTotals:Total:grandTotalCombinedPartTimeOutOfState", "label": "Grand Total - Part Time", "type": "number", "currentValue": 0, "spreadsheetCell": "A246", "section": "Out-of-State", "comments": ""}, "grandTotalCombinedFTEOutOfState": {"path": "Out-of-State:GrandTotals:Total:grandTotalCombinedFTEOutOfState", "label": "Grand Total - Total FTE", "type": "number", "currentValue": 0, "spreadsheetCell": "A247", "section": "Out-of-State", "comments": ""}, "firstTimeFreshmenFirstTime": {"path": "Totals:New Admissions:First-time:firstTimeFreshmenFirstTime", "label": "First-Time Freshmen", "type": "number", "currentValue": 0, "spreadsheetCell": "A248", "section": "Totals", "comments": ""}, "otherFreshmenFirstTime": {"path": "Totals:New Admissions:First-time:otherFreshmenFirstTime", "label": "Other Freshmen", "type": "number", "currentValue": 0, "spreadsheetCell": "A249", "section": "Totals", "comments": ""}, "sophomoreFirstTime": {"path": "Totals:New Admissions:First-time:sophomore<PERSON><PERSON>tTime", "label": "Sophomore", "type": "number", "currentValue": 0, "spreadsheetCell": "A250", "section": "Totals", "comments": ""}, "juniorFirstTime": {"path": "Totals:New Admissions:First-time:juniorFirstTime", "label": "Junior", "type": "number", "currentValue": 0, "spreadsheetCell": "A251", "section": "Totals", "comments": ""}, "seniorFifthYrFirstTime": {"path": "Totals:New Admissions:First-time:seniorFifthYrFirstTime", "label": "Senior & 5th Yr.", "type": "number", "currentValue": 0, "spreadsheetCell": "A252", "section": "Totals", "comments": ""}, "specUnclassFirstTime": {"path": "Totals:New Admissions:First-time:specUnclassFirstTime", "label": "Spec./Unclass.", "type": "number", "currentValue": 0, "spreadsheetCell": "A253", "section": "Totals", "comments": ""}, "undergradTotalFirstTime": {"path": "Totals:New Admissions:First-time:undergradTotalFirstTime", "label": "Undergrad Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A254", "section": "Totals", "comments": ""}, "medicineFirstTime": {"path": "Totals:New Admissions:First-time:medicineFirstTime", "label": "Medicine", "type": "number", "currentValue": 0, "spreadsheetCell": "A255", "section": "Totals", "comments": ""}, "pharmacyFirstTime": {"path": "Totals:New Admissions:First-time:pharmacyFirstTime", "label": "Pharmacy", "type": "number", "currentValue": 0, "spreadsheetCell": "A256", "section": "Totals", "comments": ""}, "lawFirstTime": {"path": "Totals:New Admissions:First-time:lawFirstTime", "label": "Law", "type": "number", "currentValue": 0, "spreadsheetCell": "A257", "section": "Totals", "comments": ""}, "theologyFirstTime": {"path": "Totals:New Admissions:First-time:theologyFirstTime", "label": "Theology", "type": "number", "currentValue": 0, "spreadsheetCell": "A258", "section": "Totals", "comments": ""}, "physicalTherapyFirstTime": {"path": "Totals:New Admissions:First-time:physicalTherapyFirstTime", "label": "Physical Therapy", "type": "number", "currentValue": 0, "spreadsheetCell": "A259", "section": "Totals", "comments": ""}, "nursePractitionFirstTime": {"path": "Totals:New Admissions:First-time:nursePractitionFirstTime", "label": "Nurse Practition", "type": "number", "currentValue": 0, "spreadsheetCell": "A260", "section": "Totals", "comments": ""}, "osteopathicMedicineFirstTime": {"path": "Totals:New Admissions:First-time:osteopathicMedicineFirstTime", "label": "Osteopathic Medicine", "type": "number", "currentValue": 0, "spreadsheetCell": "A261", "section": "Totals", "comments": ""}, "docProfTotalFirstTime": {"path": "Totals:New Admissions:First-time:docProfTotalFirstTime", "label": "Doc. Prof. Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A262", "section": "Totals", "comments": ""}, "mastersFirstTime": {"path": "Totals:New Admissions:First-time:mastersFirstTime", "label": "Master's", "type": "number", "currentValue": 0, "spreadsheetCell": "A263", "section": "Totals", "comments": ""}, "doctoralResearchFirstTime": {"path": "Totals:New Admissions:First-time:doctoralResearchFirstTime", "label": "Doctoral Research", "type": "number", "currentValue": 0, "spreadsheetCell": "A264", "section": "Totals", "comments": ""}, "specUnclassGradFirstTime": {"path": "Totals:New Admissions:First-time:specUnclassGradFirstTime", "label": "Spec./Unclass.", "type": "number", "currentValue": 0, "spreadsheetCell": "A265", "section": "Totals", "comments": ""}, "mastDocResTotalFirstTime": {"path": "Totals:New Admissions:First-time:mastDocResTotalFirstTime", "label": "Mast.& Doc. Res. Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A266", "section": "Totals", "comments": ""}, "grandTotalFirstTime": {"path": "Totals:New Admissions:First-time:grandTotalFirstTime", "label": "Grand Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A267", "section": "Totals", "comments": ""}, "firstTimeFreshmenNewTransfers": {"path": "Totals:New Admissions:New Transfers:firstTimeFreshmenNewTransfers", "label": "First-Time Freshmen", "type": "number", "currentValue": 0, "spreadsheetCell": "A268", "section": "Totals", "comments": ""}, "otherFreshmenNewTransfers": {"path": "Totals:New Admissions:New Transfers:otherFreshmenNewTransfers", "label": "Other Freshmen", "type": "number", "currentValue": 0, "spreadsheetCell": "A269", "section": "Totals", "comments": ""}, "sophomoreNewTransfers": {"path": "Totals:New Admissions:New Transfers:sophomoreNewTransfers", "label": "Sophomore", "type": "number", "currentValue": 0, "spreadsheetCell": "A270", "section": "Totals", "comments": ""}, "juniorNewTransfers": {"path": "Totals:New Admissions:New Transfers:juniorNewTransfers", "label": "Junior", "type": "number", "currentValue": 0, "spreadsheetCell": "A271", "section": "Totals", "comments": ""}, "seniorFifthYrNewTransfers": {"path": "Totals:New Admissions:New Transfers:seniorFifthYrNewTransfers", "label": "Senior & 5th Yr.", "type": "number", "currentValue": 0, "spreadsheetCell": "A272", "section": "Totals", "comments": ""}, "specUnclassNewTransfers": {"path": "Totals:New Admissions:New Transfers:specUnclassNewTransfers", "label": "Spec./Unclass.", "type": "number", "currentValue": 0, "spreadsheetCell": "A273", "section": "Totals", "comments": ""}, "undergradTotalNewTransfers": {"path": "Totals:New Admissions:New Transfers:undergradTotalNewTransfers", "label": "Undergrad Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A274", "section": "Totals", "comments": ""}, "medicineNewTransfers": {"path": "Totals:New Admissions:New Transfers:medicineNewTransfers", "label": "Medicine", "type": "number", "currentValue": 0, "spreadsheetCell": "A275", "section": "Totals", "comments": ""}, "pharmacyNewTransfers": {"path": "Totals:New Admissions:New Transfers:pharmacyNewTransfers", "label": "Pharmacy", "type": "number", "currentValue": 0, "spreadsheetCell": "A276", "section": "Totals", "comments": ""}, "lawNewTransfers": {"path": "Totals:New Admissions:New Transfers:lawNewTransfers", "label": "Law", "type": "number", "currentValue": 0, "spreadsheetCell": "A277", "section": "Totals", "comments": ""}, "theologyNewTransfers": {"path": "Totals:New Admissions:New Transfers:theologyNewTransfers", "label": "Theology", "type": "number", "currentValue": 0, "spreadsheetCell": "A278", "section": "Totals", "comments": ""}, "physicalTherapyNewTransfers": {"path": "Totals:New Admissions:New Transfers:physicalTherapyNewTransfers", "label": "Physical Therapy", "type": "number", "currentValue": 0, "spreadsheetCell": "A279", "section": "Totals", "comments": ""}, "nursePractitionNewTransfers": {"path": "Totals:New Admissions:New Transfers:nursePractitionNewTransfers", "label": "Nurse Practition", "type": "number", "currentValue": 0, "spreadsheetCell": "A280", "section": "Totals", "comments": ""}, "osteopathicMedicineNewTransfers": {"path": "Totals:New Admissions:New Transfers:osteopathicMedicineNewTransfers", "label": "Osteopathic Medicine", "type": "number", "currentValue": 0, "spreadsheetCell": "A281", "section": "Totals", "comments": ""}, "docProfTotalNewTransfers": {"path": "Totals:New Admissions:New Transfers:docProfTotalNewTransfers", "label": "Doc. Prof. Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A282", "section": "Totals", "comments": ""}, "mastersNewTransfers": {"path": "Totals:New Admissions:New Transfers:mastersNewTransfers", "label": "Master's", "type": "number", "currentValue": 0, "spreadsheetCell": "A283", "section": "Totals", "comments": ""}, "doctoralResearchNewTransfers": {"path": "Totals:New Admissions:New Transfers:doctoralResearchNewTransfers", "label": "Doctoral Research", "type": "number", "currentValue": 0, "spreadsheetCell": "A284", "section": "Totals", "comments": ""}, "specUnclassGradNewTransfers": {"path": "Totals:New Admissions:New Transfers:specUnclassGradNewTransfers", "label": "Spec./Unclass.", "type": "number", "currentValue": 0, "spreadsheetCell": "A285", "section": "Totals", "comments": ""}, "mastDocResTotalNewTransfers": {"path": "Totals:New Admissions:New Transfers:mastDocResTotalNewTransfers", "label": "Mast.& Doc. Res. Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A286", "section": "Totals", "comments": ""}, "grandTotalNewTransfers": {"path": "Totals:New Admissions:New Transfers:grandTotalNewTransfers", "label": "Grand Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A287", "section": "Totals", "comments": ""}, "firstTimeFreshmenReturning": {"path": "Totals:New Admissions:Returning:firstTimeFreshmenReturning", "label": "First-Time Freshmen", "type": "number", "currentValue": 0, "spreadsheetCell": "A288", "section": "Totals", "comments": ""}, "otherFreshmenReturning": {"path": "Totals:New Admissions:Returning:otherFreshmenReturning", "label": "Other Freshmen", "type": "number", "currentValue": 0, "spreadsheetCell": "A289", "section": "Totals", "comments": ""}, "sophomoreReturning": {"path": "Totals:New Admissions:Returning:sophomore<PERSON><PERSON><PERSON>", "label": "Sophomore", "type": "number", "currentValue": 0, "spreadsheetCell": "A290", "section": "Totals", "comments": ""}, "juniorReturning": {"path": "Totals:New Admissions:Returning:juniorReturning", "label": "Junior", "type": "number", "currentValue": 0, "spreadsheetCell": "A291", "section": "Totals", "comments": ""}, "seniorFifthYrReturning": {"path": "Totals:New Admissions:Returning:seniorFifthYrReturning", "label": "Senior & 5th Yr.", "type": "number", "currentValue": 0, "spreadsheetCell": "A292", "section": "Totals", "comments": ""}, "specUnclassReturning": {"path": "Totals:New Admissions:Returning:specUnclassReturning", "label": "Spec./Unclass.", "type": "number", "currentValue": 0, "spreadsheetCell": "A293", "section": "Totals", "comments": ""}, "undergradTotalReturning": {"path": "Totals:New Admissions:Returning:undergradTotalReturning", "label": "Undergrad Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A294", "section": "Totals", "comments": ""}, "medicineReturning": {"path": "Totals:New Admissions:Returning:medicineReturning", "label": "Medicine", "type": "number", "currentValue": 0, "spreadsheetCell": "A295", "section": "Totals", "comments": ""}, "pharmacyReturning": {"path": "Totals:New Admissions:Returning:pharmacyReturning", "label": "Pharmacy", "type": "number", "currentValue": 0, "spreadsheetCell": "A296", "section": "Totals", "comments": ""}, "lawReturning": {"path": "Totals:New Admissions:Returning:lawReturning", "label": "Law", "type": "number", "currentValue": 0, "spreadsheetCell": "A297", "section": "Totals", "comments": ""}, "theologyReturning": {"path": "Totals:New Admissions:Returning:theologyReturning", "label": "Theology", "type": "number", "currentValue": 0, "spreadsheetCell": "A298", "section": "Totals", "comments": ""}, "physicalTherapyReturning": {"path": "Totals:New Admissions:Returning:physicalTherapyReturning", "label": "Physical Therapy", "type": "number", "currentValue": 0, "spreadsheetCell": "A299", "section": "Totals", "comments": ""}, "nursePractitionReturning": {"path": "Totals:New Admissions:Returning:nursePractitionReturning", "label": "Nurse Practition", "type": "number", "currentValue": 0, "spreadsheetCell": "A300", "section": "Totals", "comments": ""}, "osteopathicMedicineReturning": {"path": "Totals:New Admissions:Returning:osteopathicMedicineReturning", "label": "Osteopathic Medicine", "type": "number", "currentValue": 0, "spreadsheetCell": "A301", "section": "Totals", "comments": ""}, "docProfTotalReturning": {"path": "Totals:New Admissions:Returning:docProfTotalReturning", "label": "Doc. Prof. Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A302", "section": "Totals", "comments": ""}, "mastersReturning": {"path": "Totals:New Admissions:Returning:mastersReturning", "label": "Master's", "type": "number", "currentValue": 0, "spreadsheetCell": "A303", "section": "Totals", "comments": ""}, "doctoralResearchReturning": {"path": "Totals:New Admissions:Returning:doctoralResearchReturning", "label": "Doctoral Research", "type": "number", "currentValue": 0, "spreadsheetCell": "A304", "section": "Totals", "comments": ""}, "specUnclassGradReturning": {"path": "Totals:New Admissions:Returning:specUnclassGradReturning", "label": "Spec./Unclass.", "type": "number", "currentValue": 0, "spreadsheetCell": "A305", "section": "Totals", "comments": ""}, "mastDocResTotalReturning": {"path": "Totals:New Admissions:Returning:mastDocResTotalReturning", "label": "Mast.& Doc. Res. Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A306", "section": "Totals", "comments": ""}, "grandTotalReturning": {"path": "Totals:New Admissions:Returning:grandTotalReturning", "label": "Grand Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A307", "section": "Totals", "comments": ""}, "firstTimeFreshmenContinuing": {"path": "Totals:New Admissions:Continuing:firstTimeFreshmenContinuing", "label": "First-Time Freshmen", "type": "number", "currentValue": 0, "spreadsheetCell": "A308", "section": "Totals", "comments": ""}, "otherFreshmenContinuing": {"path": "Totals:New Admissions:Continuing:otherFreshmenContinuing", "label": "Other Freshmen", "type": "number", "currentValue": 0, "spreadsheetCell": "A309", "section": "Totals", "comments": ""}, "sophomoreContinuing": {"path": "Totals:New Admissions:Continuing:sophomore<PERSON><PERSON><PERSON><PERSON>", "label": "Sophomore", "type": "number", "currentValue": 0, "spreadsheetCell": "A310", "section": "Totals", "comments": ""}, "juniorContinuing": {"path": "Totals:New Admissions:Continuing:junior<PERSON>ontinuing", "label": "Junior", "type": "number", "currentValue": 0, "spreadsheetCell": "A311", "section": "Totals", "comments": ""}, "seniorFifthYrContinuing": {"path": "Totals:New Admissions:Continuing:seniorFifthYrContinuing", "label": "Senior & 5th Yr.", "type": "number", "currentValue": 0, "spreadsheetCell": "A312", "section": "Totals", "comments": ""}, "specUnclassContinuing": {"path": "Totals:New Admissions:Continuing:specUnclassContinuing", "label": "Spec./Unclass.", "type": "number", "currentValue": 0, "spreadsheetCell": "A313", "section": "Totals", "comments": ""}, "undergradTotalContinuing": {"path": "Totals:New Admissions:Continuing:undergradTotalContinuing", "label": "Undergrad Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A314", "section": "Totals", "comments": ""}, "medicineContinuing": {"path": "Totals:New Admissions:Continuing:medicineContinuing", "label": "Medicine", "type": "number", "currentValue": 0, "spreadsheetCell": "A315", "section": "Totals", "comments": ""}, "pharmacyContinuing": {"path": "Totals:New Admissions:Continuing:pharmacyContinuing", "label": "Pharmacy", "type": "number", "currentValue": 0, "spreadsheetCell": "A316", "section": "Totals", "comments": ""}, "lawContinuing": {"path": "Totals:New Admissions:Continuing:lawContinuing", "label": "Law", "type": "number", "currentValue": 0, "spreadsheetCell": "A317", "section": "Totals", "comments": ""}, "theologyContinuing": {"path": "Totals:New Admissions:Continuing:theologyContinuing", "label": "Theology", "type": "number", "currentValue": 0, "spreadsheetCell": "A318", "section": "Totals", "comments": ""}, "physicalTherapyContinuing": {"path": "Totals:New Admissions:Continuing:physicalTherapyContinuing", "label": "Physical Therapy", "type": "number", "currentValue": 0, "spreadsheetCell": "A319", "section": "Totals", "comments": ""}, "nursePractitionContinuing": {"path": "Totals:New Admissions:Continuing:nursePractitionContinuing", "label": "Nurse Practition", "type": "number", "currentValue": 0, "spreadsheetCell": "A320", "section": "Totals", "comments": ""}, "osteopathicMedicineContinuing": {"path": "Totals:New Admissions:Continuing:osteopathicMedicineContinuing", "label": "Osteopathic Medicine", "type": "number", "currentValue": 0, "spreadsheetCell": "A321", "section": "Totals", "comments": ""}, "docProfTotalContinuing": {"path": "Totals:New Admissions:Continuing:docProfTotalContinuing", "label": "Doc. Prof. Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A322", "section": "Totals", "comments": ""}, "mastersContinuing": {"path": "Totals:New Admissions:Continuing:mastersContinuing", "label": "Master's", "type": "number", "currentValue": 0, "spreadsheetCell": "A323", "section": "Totals", "comments": ""}, "doctoralResearchContinuing": {"path": "Totals:New Admissions:Continuing:doctoralResearchContinuing", "label": "Doctoral Research", "type": "number", "currentValue": 0, "spreadsheetCell": "A324", "section": "Totals", "comments": ""}, "specUnclassGradContinuing": {"path": "Totals:New Admissions:Continuing:specUnclassGradContinuing", "label": "Spec./Unclass.", "type": "number", "currentValue": 0, "spreadsheetCell": "A325", "section": "Totals", "comments": ""}, "mastDocResTotalContinuing": {"path": "Totals:New Admissions:Continuing:mastDocResTotalContinuing", "label": "Mast.& Doc. Res. Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A326", "section": "Totals", "comments": ""}, "grandTotalContinuing": {"path": "Totals:New Admissions:Continuing:grandTotalContinuing", "label": "Grand Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A327", "section": "Totals", "comments": ""}, "firstTimeFreshmenHeadCount": {"path": "Totals:Grand Totals:Head Count:firstTimeFreshmenHeadCount", "label": "First-Time Freshmen", "type": "number", "currentValue": 0, "spreadsheetCell": "A328", "section": "Totals", "comments": ""}, "otherFreshmenHeadCount": {"path": "Totals:Grand Totals:Head Count:otherFreshmenHeadCount", "label": "Other Freshmen", "type": "number", "currentValue": 0, "spreadsheetCell": "A329", "section": "Totals", "comments": ""}, "sophomoreHeadCount": {"path": "Totals:Grand Totals:Head Count:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "Sophomore", "type": "number", "currentValue": 0, "spreadsheetCell": "A330", "section": "Totals", "comments": ""}, "juniorHeadCount": {"path": "Totals:Grand Totals:Head Count:<PERSON><PERSON><PERSON><PERSON><PERSON>nt", "label": "Junior", "type": "number", "currentValue": 0, "spreadsheetCell": "A331", "section": "Totals", "comments": ""}, "seniorFifthYrHeadCount": {"path": "Totals:Grand Totals:Head Count:<PERSON><PERSON><PERSON>hYrHeadCount", "label": "Senior & 5th Yr.", "type": "number", "currentValue": 0, "spreadsheetCell": "A332", "section": "Totals", "comments": ""}, "specUnclassHeadCount": {"path": "Totals:Grand Totals:Head Count:specUnclassHeadCount", "label": "Spec./Unclass.", "type": "number", "currentValue": 0, "spreadsheetCell": "A333", "section": "Totals", "comments": ""}, "undergradTotalHeadCount": {"path": "Totals:Grand Totals:Head Count:undergradTotalHeadCount", "label": "Undergrad Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A334", "section": "Totals", "comments": ""}, "medicineHeadCount": {"path": "Totals:Grand Totals:Head Count:medicineHeadCount", "label": "Medicine", "type": "number", "currentValue": 0, "spreadsheetCell": "A335", "section": "Totals", "comments": ""}, "pharmacyHeadCount": {"path": "Totals:Grand Totals:Head Count:pharmacyHeadCount", "label": "Pharmacy", "type": "number", "currentValue": 0, "spreadsheetCell": "A336", "section": "Totals", "comments": ""}, "lawHeadCount": {"path": "Totals:Grand Totals:Head Count:lawHeadCount", "label": "Law", "type": "number", "currentValue": 0, "spreadsheetCell": "A337", "section": "Totals", "comments": ""}, "theologyHeadCount": {"path": "Totals:Grand Totals:Head Count:theologyHeadCount", "label": "Theology", "type": "number", "currentValue": 0, "spreadsheetCell": "A338", "section": "Totals", "comments": ""}, "physicalTherapyHeadCount": {"path": "Totals:Grand Totals:Head Count:physicalTherapyHeadCount", "label": "Physical Therapy", "type": "number", "currentValue": 0, "spreadsheetCell": "A339", "section": "Totals", "comments": ""}, "nursePractitionHeadCount": {"path": "Totals:Grand Totals:Head Count:nursePractitionHeadCount", "label": "Nurse Practition", "type": "number", "currentValue": 0, "spreadsheetCell": "A340", "section": "Totals", "comments": ""}, "osteopathicMedicineHeadCount": {"path": "Totals:Grand Totals:Head Count:osteopathicMedicineHeadCount", "label": "Osteopathic Medicine", "type": "number", "currentValue": 0, "spreadsheetCell": "A341", "section": "Totals", "comments": ""}, "docProfTotalHeadCount": {"path": "Totals:Grand Totals:Head Count:docProfTotalHeadCount", "label": "Doc. Prof. Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A342", "section": "Totals", "comments": ""}, "mastersHeadCount": {"path": "Totals:Grand Totals:Head Count:mastersHeadCount", "label": "Master's", "type": "number", "currentValue": 0, "spreadsheetCell": "A343", "section": "Totals", "comments": ""}, "doctoralResearchHeadCount": {"path": "Totals:Grand Totals:Head Count:doctoralResearchHeadCount", "label": "Doctoral Research", "type": "number", "currentValue": 0, "spreadsheetCell": "A344", "section": "Totals", "comments": ""}, "specUnclassGradHeadCount": {"path": "Totals:Grand Totals:Head Count:specUnclassGradHeadCount", "label": "Spec./Unclass.", "type": "number", "currentValue": 0, "spreadsheetCell": "A345", "section": "Totals", "comments": ""}, "mastDocResTotalHeadCount": {"path": "Totals:Grand Totals:Head Count:mastDocResTotalHeadCount", "label": "Mast.& Doc. Res. Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A346", "section": "Totals", "comments": ""}, "grandTotalHeadCount": {"path": "Totals:Grand Totals:Head Count:grandTotalHeadCount", "label": "Grand Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A347", "section": "Totals", "comments": ""}, "seniorFifthYrFTE": {"path": "Totals:Grand Totals:FTE:seniorFifthYrFTE", "label": "Senior & 5th Yr.", "type": "number", "currentValue": 0, "spreadsheetCell": "A352", "section": "Totals", "comments": ""}, "specUnclassFTE": {"path": "Totals:Grand Totals:FTE:specUnclassFTE", "label": "Spec./Unclass.", "type": "number", "currentValue": 0, "spreadsheetCell": "A353", "section": "Totals", "comments": ""}, "specUnclassGradFTE": {"path": "Totals:Grand Totals:FTE:specUnclassGradFTE", "label": "Spec./Unclass.", "type": "number", "currentValue": 0, "spreadsheetCell": "A365", "section": "Totals", "comments": ""}, "grandTotalFTE": {"path": "Totals:Grand Totals:FTE:grandTotalFTE", "label": "Grand Total", "type": "number", "currentValue": 0, "spreadsheetCell": "A367", "section": "Totals", "comments": ""}, "ficeCode": {"path": "institution:ficeCode", "label": "FICE Code", "type": "text", "currentValue": "2910", "spreadsheetCell": "A368", "section": "institution", "comments": ""}, "ipedsUnitid": {"path": "institution:ipedsUnitid", "label": "IPEDS Unit ID", "type": "text", "currentValue": "1979841", "spreadsheetCell": "A369", "section": "institution", "comments": ""}, "institutionName": {"path": "institution:institutionName", "label": "Institution Name", "type": "text", "currentValue": "Belmont Abbey College", "spreadsheetCell": "A370", "section": "institution", "comments": ""}, "institutionType": {"path": "institution:institutionType", "label": "Institution Type", "type": "text", "currentValue": "Private, 4 year, Not for Profit", "spreadsheetCell": "A371", "section": "institution", "comments": ""}, "reportingPersonName": {"path": "reportingPerson:reportingPersonName", "label": "Name", "type": "text", "currentValue": "<PERSON><PERSON>", "spreadsheetCell": "A372", "section": "reporting<PERSON><PERSON>", "comments": ""}, "reportingPersonTitle": {"path": "reportingPerson:reportingPersonTitle", "label": "Title", "type": "text", "currentValue": "Assistant Vice Provost for Data Management and IR", "spreadsheetCell": "A373", "section": "reporting<PERSON><PERSON>", "comments": ""}, "reportingPersonEmail": {"path": "reportingPerson:reportingPersonEmail", "label": "Email", "type": "text", "currentValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>@bac.edu", "spreadsheetCell": "A374", "section": "reporting<PERSON><PERSON>", "comments": ""}, "reportingPersonTelephone": {"path": "reportingPerson:reportingPersonTelephone", "label": "Telephone", "type": "text", "currentValue": "************", "spreadsheetCell": "A375", "section": "reporting<PERSON><PERSON>", "comments": ""}, "reportingPersonFax": {"path": "reportingPerson:reportingPersonFax", "label": "Fax", "type": "tel", "currentValue": "************", "spreadsheetCell": "A376", "section": "reporting<PERSON><PERSON>", "comments": ""}, "submissionTitle": {"path": "submission:submission<PERSON><PERSON><PERSON>", "label": "Submission", "type": "text", "currentValue": "2019-2020 NCHED Submission", "spreadsheetCell": "A377", "section": "submission", "comments": ""}, "nchedA11Title": {"path": "Enrollment:nchedA11Title", "label": "NCHED A-1.1: Fall 2019 Degree Credit Enrollment", "type": "text", "currentValue": "NCHED A-1.1: Fall 2019 Degree Credit Enrollment", "spreadsheetCell": "A378", "section": "Enrollment", "comments": ""}}}