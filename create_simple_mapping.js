// Script to create simple spreadsheet mapping from form.json
const fs = require('fs');
const FormFiller = require('./form_filler.js');

// Load the form data
const formData = JSON.parse(fs.readFileSync('form.json', 'utf8'));

// Create form filler instance
const filler = new FormFiller(formData);

// Get all paths
const allPaths = filler.getAllPaths();

// Create simple mapping array
const mapping = [];

// Start from row 2 (assuming row 1 has headers)
let currentRow = 2;

// Create simple mappings
allPaths.forEach((item, index) => {
  const cellRef = `A${currentRow}`;
  
  mapping.push({
    "key": item.path,
    "cell": cellRef
  });
  
  currentRow++;
});

// Save the simple mapping file
fs.writeFileSync('simple_mapping.json', JSON.stringify(mapping, null, 2));

console.log(`Created simple mapping file with ${mapping.length} entries`);
console.log('Simple mapping saved to simple_mapping.json');

// Also create a preview of first 10 mappings
console.log('\nFirst 10 mappings:');
mapping.slice(0, 10).forEach(item => {
  console.log(`${item.cell}: ${item.key}`);
});
