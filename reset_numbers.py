#!/usr/bin/env python3
import json

def reset_number_values(data):
    """Recursively traverse the JSON and set all number field values to 0"""
    if isinstance(data, dict):
        # Check if this is a form item with type "number"
        if all(key in data for key in ['key', 'label', 'type', 'value']) and data.get('type') == 'number':
            # Set value to 0
            data['value'] = 0
        
        # Recursively process all dictionary values
        for key, value in data.items():
            reset_number_values(value)
    
    elif isinstance(data, list):
        # Recursively process all list items
        for item in data:
            reset_number_values(item)

def main():
    # Read the JSON file
    with open('form.json', 'r') as f:
        data = json.load(f)
    
    # Reset all number values to 0
    reset_number_values(data)
    
    # Write back to file with proper formatting
    with open('form.json', 'w') as f:
        json.dump(data, f, indent=2)
    
    print("Successfully reset all number field values to 0!")

if __name__ == '__main__':
    main()
